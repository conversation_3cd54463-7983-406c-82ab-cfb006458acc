/* Custom CSS for Judge.me reviews */

/* Override the existing styles for the reviews title to make it an h2 */
.jdgm-rev-widg__title,
h2.jdgm-rev-widg__title,
.jdgm-rev-widg__title.h2,
h2.jdgm-rev-widg__title.h2 {
  display: block !important;
  visibility: visible !important;
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-size: var(--text-h2) !important;
  line-height: 1.1 !important;
  margin-bottom: 1.5rem !important;
  color: rgb(var(--text-color)) !important;
  text-transform: var(--heading-text-transform) !important;
}

/* Ensure proper desktop sizing with higher specificity */
@media screen and (min-width: 700px) {
  .jdgm-rev-widg .jdgm-rev-widg__title,
  .jdgm-rev-widg h2.jdgm-rev-widg__title,
  .jdgm-rev-widg .jdgm-rev-widg__title.h2,
  .jdgm-rev-widg h2.jdgm-rev-widg__title.h2,
  #judgeme_product_reviews .jdgm-rev-widg__title,
  #judgeme_product_reviews h2.jdgm-rev-widg__title,
  #judgeme_product_reviews .jdgm-rev-widg__title.h2,
  #judgeme_product_reviews h2.jdgm-rev-widg__title.h2,
  .shopify-section .jdgm-rev-widg__title,
  .shopify-section h2.jdgm-rev-widg__title,
  .shopify-section .jdgm-rev-widg__title.h2,
  .shopify-section h2.jdgm-rev-widg__title.h2,
  [id*="judgeme"] .jdgm-rev-widg__title,
  [id*="judgeme"] h2.jdgm-rev-widg__title,
  [id*="judgeme"] .jdgm-rev-widg__title.h2,
  [id*="judgeme"] h2.jdgm-rev-widg__title.h2 {
    font-size: var(--text-h2) !important;
    font-family: var(--heading-font-family) !important;
    font-weight: var(--heading-font-weight) !important;
    line-height: 1.1 !important;
    text-transform: var(--heading-text-transform) !important;
  }
}

/* Additional fallback for larger screens */
@media screen and (min-width: 1400px) {
  .jdgm-rev-widg .jdgm-rev-widg__title,
  .jdgm-rev-widg h2.jdgm-rev-widg__title,
  .jdgm-rev-widg .jdgm-rev-widg__title.h2,
  .jdgm-rev-widg h2.jdgm-rev-widg__title.h2,
  #judgeme_product_reviews .jdgm-rev-widg__title,
  #judgeme_product_reviews h2.jdgm-rev-widg__title,
  #judgeme_product_reviews .jdgm-rev-widg__title.h2,
  #judgeme_product_reviews h2.jdgm-rev-widg__title.h2,
  .shopify-section .jdgm-rev-widg__title,
  .shopify-section h2.jdgm-rev-widg__title,
  .shopify-section .jdgm-rev-widg__title.h2,
  .shopify-section h2.jdgm-rev-widg__title.h2,
  [id*="judgeme"] .jdgm-rev-widg__title,
  [id*="judgeme"] h2.jdgm-rev-widg__title,
  [id*="judgeme"] .jdgm-rev-widg__title.h2,
  [id*="judgeme"] h2.jdgm-rev-widg__title.h2 {
    font-size: var(--text-h2) !important;
  }
}

/* Make sure the title is properly displayed */
.jdgm-widget-actions-wrapper {
  margin-top: 1.5rem !important;
}

/* Ensure proper spacing around the reviews widget */
.jdgm-rev-widg {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
  padding: 0 !important;
}

span.jdgm-prev-badge__text {
  font-family: 'FormaDJRText';
}
.jdgm-rev-widg__summary-inner {
  font-family: 'FormaDJRText';
}

.jdgm-rev-widg__sort-wrapper {
  font-family: 'FormaDJRText';
}